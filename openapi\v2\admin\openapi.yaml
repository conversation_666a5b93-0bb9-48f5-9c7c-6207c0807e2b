openapi: 3.0.0
info:
  title: Easylearning Admin API v2
  description: API for Easylearning administration
  version: 2.0.0

servers:
  - url: /api/v2/admin
    description: Admin server v2

security:
  - bearerAuth: []

paths:
  /users:
    get:
      tags:
        - Users
      summary: Get users list
      description: |
        Retrieves a paginated list of users with filtering capabilities.
        Filters work as AND between different categories and OR within the same category.
        If the requesting user is a manager and not an admin, users will be filtered
        according to their assigned filters, also adding users they have created.
      operationId: getUsers
      parameters:
        - name: page
          in: query
          description: Page number (starts at 1)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            default: 10
        - name: is_active
          in: query
          description: Filter by active status
          required: false
          schema:
            type: string
            enum: [true, false]
        - name: start_date
          in: query
          description: Filter by start date (format Y-m-d H:i:s)
          required: false
          schema:
            type: string
            format: date-time
        - name: end_date
          in: query
          description: Filter by end date (format Y-m-d H:i:s)
          required: false
          schema:
            type: string
            format: date-time
        - name: search
          in: query
          description: Search by text (first name, last name, email)
          required: false
          schema:
            type: string
        - name: role
          in: query
          description: Filter by role
          required: false
          schema:
            type: string
        - name: sort_by
          in: query
          description: Field to sort by (id, first_name, email)
          required: false
          schema:
            type: string
            enum: [id, first_name, email]
            default: id
        - name: sort_dir
          in: query
          description: Sort direction
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Paginated list of users
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/User'
                  metadata:
                    $ref: '#/components/schemas/Pagination'
        '400':
          description: Invalid query parameters
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
  /users/{userId}/manager-filters:
    options:
      tags:
        - Users
        - Filters
      summary: Check if the endpoint is available
      responses:
        204:
          description: Manager filters endpoint available
    delete:
      tags:
        - Users
        - Filters
      summary: DELETE manager filters
      description: Delete a list of managed filters from a user
      operationId: deleteManagerFilters
      parameters:
        - name: userId
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            minimum: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                format: int64
                description: Filter Unique ID
                minimum: 1
      responses:
        204:
          description: Manager filters deleted successfully
        400:
          description: Bad user id or bad filter ids
        401:
          description: Unauthorized
        403:
          description: Forbidden. Only ADMIN
        404:
          description: User not found
        422:
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                filterDoesNotExists:
                  summary: The filter does not exists in database.
                  value:
                    message: "At least one of the filters does not exist"
                filterAlreadyAssigned:
                  summary: The filter is not assigned to the user
                  value:
                    message: "At least one of the filters is not assigned to a user"
        500:
          description: Internal Server Error
    post:
      tags:
        - Users
        - Filters
      summary: POST manager filters
      description: Save a list of existing filters to a user to manage
      operationId: postManagerFilters
      parameters:
        - name: userId
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            minimum: 1
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                format: int64
                description: Filter Unique ID
                minimum: 1
      responses:
        '200':
          description: User filters saved successfully
        '400':
          description: Bad user id or bad filters ids
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '403':
          description: Forbidden, only Admin is allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                filterDoesNotExists:
                  summary: The filter does not exists in database.
                  value:
                    message: "At least one of the filters does not exist"
                filterAlreadyAssigned:
                  summary: The filter is already assigned to the user
                  value:
                    message: "At least one of the filters is already assigned to a user"
        '500':
          description: Internal Server Error
    get:
      tags:
        - Users
        - Filters
      summary: GET manager filters
      description: Get a list of filters the user can manage
      operationId: getManagerFilters
      parameters:
        - name: userId
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: List of assigned filters to manage
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/ManagerFilter'
        400:
          description: Bad user id
        401:
          description: Unauthorized
        403:
          description: Forbidden. Only admin
        404:
          description: User not found
        422:
          description: Unprocessable Entity
        500:
          description: Internal Server Error
  /users/{userId}/filters:
    options:
      tags:
        - Users
        - Filters
      summary: Check if the endpoint is available
      responses:
        204:
          description: User filters endpoint available
    get:
      tags:
        - Users
        - Filters
      summary: GET User filters
      description: Get a list of assigned filters to a user
      operationId: getUserFilters
      parameters:
        - name: userId
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: List of assigned filters
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/UserFilter'
        400:
          description: Bad user id
        401:
          description: Unauthorized
        403:
          description: Forbidden. Only admin or manager
        404:
          description: User not found
        422:
          description: Unprocessable Entity
        500:
          description: Internal Server Error
    post:
      tags:
        - Users
        - Filters
      summary: POST user filters
      description: Save a list of existing filters to a user
      operationId: postUserFilters
      parameters:
        - name: userId
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            minimum: 1
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                format: int64
                description: Filter Unique ID
      responses:
        200:
          description: User filters saved successfully
        400:
          description: Bad user id or bad filters ids
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        403:
          description: Forbidden, only Admin or managers allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        422:
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                filterDoesNotExists:
                  summary: The filter does not exists in database.
                  value:
                    message: "At least one of the filters does not exist"
                cannotAssignTheFilter:
                  summary: If the user is manager and does not have access to the filter
                  value:
                    message: "At least one of the filters is not managed by the user"
                filterAlreadyAssigned:
                  summary: The filter is already assigned to the user
                  value:
                    message: "At least one of the filters is already assigned to a user"
        500:
          description: Internal Server Error
    delete:
      tags:
        - Users
        - Filters
      summary: DELETE user filters
      description: Delete a list of existing filters assigned to a user
      operationId: deleteUserFilters
      parameters:
        - name: userId
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            minimum: 1
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: integer
                format: int64
                description: Filter Unique ID
      responses:
        204:
          description: User filters deleted successfully
        400:
          description: Bad user id or bad filters ids
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        403:
          description: Forbidden, only Admin or managers allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        422:
          description: Unprocessable Entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                filterDoesNotExists:
                  summary: The filter does not exists in database.
                  value:
                    message: "At least one of the filters does not exist"
                cannotAssignTheFilter:
                  summary: If the user is manager and does not have access to the filter
                  value:
                    message: "At least one of the filters is not managed by the user"
                filterAlreadyAssigned:
                  summary: The filter is not assigned to the user
                  value:
                    message: "At least one of the filters is not assigned to a user"
        500:
          description: Internal Server Error
  /users/creators:
    get:
      tags:
        - Users
      summary: Get users with role CREATOR
      description: |
        Returns a list of users that have the role CREATOR.
        This endpoint is accessible by users with roles: ADMIN, SUPER_ADMIN, or CREATOR.
      operationId: getCreatorUsers
      responses:
        '200':
          description: List of users with role CREATOR
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/MinimalUser'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
  /users/managers:
    get:
      tags:
        - Users
      summary: Get users with role MANAGER
      description: |
        Returns a paginated list of users that have the role MANAGER.
        Users are sorted alphabetically by first name and then by last name.
        This endpoint is accessible by users with roles: ADMIN, SUPER_ADMIN, or MANAGER.
      operationId: getManagerUsers
      parameters:
        - name: search
          in: query
          description: Search by text (first name, last name, email)
          required: false
          schema:
            type: string
        - name: page
          in: query
          description: Page number (starts at 1)
          required: false
          schema:
            type: integer
            minimum: 1
        - name: page_size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
      responses:
        '200':
          description: Paginated list of users with role MANAGER
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    properties:
                      users:
                        type: array
                        items:
                          $ref: '#/components/schemas/MinimalUser'
                      total:
                        type: integer
                        description: Total number of managers
        '400':
          description: Invalid query parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
  /filters:
    get:
      tags:
        - Filters
      summary: Get all filters
      description: Retrieve all available filters
      operationId: getFilters
      parameters:
        - name: search
          in: query
          required: false
          schema:
            type: string
            description: 'Filter by filter name or filter code'
        - name: parent_id
          in: query
          required: false
          schema:
            type: string
            description: 'Filter by parent filter ID'
        - name: category_id
          in: query
          required: false
          schema:
            type: string
            description: 'Filter by filter category ID'
      responses:
        '200':
          description: List of filters
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Filter'
        '400':
          description: Validation errors (Invalid name filter string)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Only Admin or manager is allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error
  /filters/categories:
    get:
      tags:
        - Filters
      summary: 'Get all filter categories'
      description: 'Retrieve all available filter categories'
      operationId: getFilterCategories
      parameters:
        - name: name
          in: query
          required: false
          schema:
            type: string
            description: 'Filter by category name'
        - name: parent_id
          in: query
          required: false
          schema:
            type: string
            description: 'Filter by parent category ID'
      responses:
        '200':
          description: List of filter categories
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/FilterCategory'
        '400':
          description: Validation errors (Invalid name filter string)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Only Admin is allowed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal Server Error

  /courses/{courseId}/creators:
    get:
      tags:
        - Courses
      summary: Get all creators of a course
      description: Retrieve a normal list of basic user information as creator for a course
      operationId: getCourseCreators
      parameters:
        - name: courseId
          in: path
          description: Course ID
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: List of course creators
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Creator'
  /courses/{courseId}/creators/{userId}:
    put:
      tags:
        - Courses
      summary: Add a creator to a course
      description: |
        Adds a creator (user with ROLE_CREATOR) to a course.
        Only administrators and the main creator of the course can perform this action.
        The user to be added must have the ROLE_CREATOR role.
      operationId: putCourseCreator
      parameters:
        - name: courseId
          in: path
          description: Course ID
          required: true
          schema:
            type: integer
            minimum: 1
        - name: userId
          in: path
          description: User ID (must be a creator)
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        '204':
          description: Creator successfully added to course
        '400':
          description: Validation errors (invalid courseId or userId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - User is not authorized to add creators to this course
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Course or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '422':
          description: Unprocessable entity
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                creatorNotFound:
                  summary: User is not a creator
                  value:
                    message: "User is not a creator"
                creatorAlreadyExists:
                  summary: Creator already associated with course
                  value:
                    message: "Creator is already associated with this course"
        '500':
          description: Internal server error
    delete:
      tags:
        - Courses
      summary: Delete a course creator
      description: Remove a creator from a course. Only admins and the course creator can use this endpoint.
      operationId: deleteCourseCreator
      parameters:
        - name: courseId
          in: path
          description: Course ID
          required: true
          schema:
            type: integer
            minimum: 1
        - name: userId
          in: path
          description: User ID of the creator to remove
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        204:
          description: Course creator deleted successfully
        400:
          description: Validation errors
        401:
          description: Unauthorized
        403:
          description: Forbidden - Insufficient permissions
        404:
          description: Course not found
        422:
          description: Unprocessable entity - CourseCreator not found
        5XX:
          description: Internal server error

  /courses/{courseId}/chapter/{chapterId}/lti-launch:
    get:
      tags:
        - Chapter
      summary: Get launch request of a lti chapter
      description: |
        Generate a request string to use against an external tool provider to execute a LTI resource
        linked to the current course and chapter.
      operationId: getLaunchLtiChapter
      parameters:
        - name: courseId
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
        - name: chapterId
          in: path
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: Get Launch request against an external tool
          content:
            text/html:
              schema:
                type: string
        400:
          description: Course Id or Chapter Id is invalid
        404:
          description: Lti Chapter not found
        422:
          description: Unprocessable Entity
        5XX:
          description: Internal server error

  /purchasable-items:
    get:
      tags:
        - Purchasable Items
      summary: Get purchasable items list
      operationId: getPurchasableItems
      parameters:
        - name: page
          in: query
          description: Page number (starts at 1)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            default: 10
        - name: search
          in: query
          description: Search term to filter items by name or description
          required: false
          schema:
            type: string
            maxLength: 255
        - name: type
          in: query
          description: Filter by resource type
          required: false
          schema:
            type: string
            enum: [course, subscription]
        - name: is_active
          in: query
          description: Filter by active status
          required: false
          schema:
            type: string
            enum: [true, false]
        - name: price_min
          in: query
          description: Minimum price filter (in cents/centavos)
          required: false
          schema:
            type: string
            pattern: '^\d+$'
        - name: price_max
          in: query
          description: Maximum price filter (in cents/centavos)
          required: false
          schema:
            type: string
            pattern: '^\d+$'
        - name: sort_by
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
            enum: [id, name, description, price_amount, is_active, created_at, updated_at]
        - name: sort_dir
          in: query
          description: Sort direction (required when sort_by is provided)
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: List of purchasable items
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PurchasableItem'
                  metadata:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: Current page number
                        example: 1
                      total_pages:
                        type: integer
                        description: Total number of pages
                        example: 5
                      limit:
                        type: integer
                        description: Items per page
                        example: 10
                      total:
                        type: integer
                        description: Total number of items
                        example: 47
                    description: Pagination metadata (only present when pagination parameters are provided)
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          description: Internal server error

  /purchasable-items/{purchasableItemId}:
    delete:
      tags:
        - Purchasable Items
      summary: Delete a purchasable item
      operationId: deletePurchasableItem
      parameters:
        - name: purchasableItemId
          in: path
          required: true
          description: UUID of the purchasable item to delete
          schema:
            $ref: '#/components/schemas/Uuid'
      responses:
        '204':
          description: Purchasable item deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '5XX':
          description: Infrastructure errors

  /announcements/{announcementId}/managers:
    get:
      tags:
        - Announcements
      summary: Get all managers of an announcement
      description: |
        Retrieve a list of managers associated with a specific announcement.
        Returns managers sorted alphabetically by name (first name + last name).
        This endpoint is only available when the 'app.announcement.managers.sharing' setting is enabled.
      operationId: getAnnouncementManagers
      parameters:
        - name: announcementId
          in: path
          description: Announcement ID to get the associated managers
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        200:
          description: List of announcement managers sorted alphabetically
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/AnnouncementManager'
        400:
          description: Validation errors (invalid announcementId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        401:
          description: Unauthorized
        403:
          description: Forbidden - Announcement manager sharing is disabled
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Announcement manager sharing is disabled"
        404:
          description: Announcement not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Announcement not found"
        500:
          description: Internal server error

  /announcements/{announcementId}/managers/{userId}:
    put:
      tags:
        - Announcements
      summary: Assign a manager to an announcement
      description: |
        Assign a user with ROLE_MANAGER to an announcement as a manager.
        Only administrators and the creator of the announcement can assign managers.
        This endpoint is only available when the 'app.announcement.managers.sharing' setting is enabled.
      operationId: putAnnouncementManager
      parameters:
        - name: announcementId
          in: path
          description: Announcement ID to assign the manager to
          required: true
          schema:
            type: integer
            minimum: 1
        - name: userId
          in: path
          description: User ID of the manager to assign
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        204:
          description: Manager successfully assigned to announcement
        400:
          description: Validation errors (invalid announcementId or userId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        401:
          description: Unauthorized
        403:
          description: |
            Forbidden - Either announcement manager sharing is disabled or user doesn't have permission to assign managers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                sharing_disabled:
                  summary: Sharing disabled
                  value:
                    message: "Announcement manager sharing is disabled"
                not_authorized:
                  summary: Not authorized
                  value:
                    message: "You do not have permission to assign managers to this announcement"
        404:
          description: Announcement not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Announcement not found"
        422:
          description: |
            Unprocessable entity - User not found, user is not a manager, user is already a manager of the announcement, or trying to assign the creator
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                user_not_found:
                  summary: User not found
                  value:
                    message: "User not found"
                not_manager:
                  summary: User is not a manager
                  value:
                    message: "User is not a manager"
                already_manager:
                  summary: Already a manager
                  value:
                    message: "User is already a manager of this announcement"
                cannot_assign_creator:
                  summary: Cannot assign creator
                  value:
                    message: "Cannot assign the creator of the announcement as a manager"
        500:
          description: Internal server error
    delete:
      tags:
        - Announcements
      summary: Remove a manager from an announcement
      description: |
        Remove a manager from an announcement.
        Only administrators and the creator of the announcement can remove managers.
        This endpoint is only available when the 'app.announcement.managers.sharing' setting is enabled.
      operationId: deleteAnnouncementManager
      parameters:
        - name: announcementId
          in: path
          description: Announcement ID to remove the manager from
          required: true
          schema:
            type: integer
            minimum: 1
        - name: userId
          in: path
          description: User ID of the manager to remove
          required: true
          schema:
            type: integer
            minimum: 1
      responses:
        204:
          description: Manager successfully removed from announcement
        400:
          description: Validation errors (invalid announcementId or userId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        401:
          description: Unauthorized
        403:
          description: |
            Forbidden - Either announcement manager sharing is disabled or user doesn't have permission to remove managers
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                sharing_disabled:
                  summary: Sharing disabled
                  value:
                    message: "Announcement manager sharing is disabled"
                not_authorized:
                  summary: Not authorized
                  value:
                    message: "You do not have permission to remove managers from this announcement"
        404:
          description: Announcement or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              examples:
                announcement_not_found:
                  summary: Announcement not found
                  value:
                    message: "Announcement not found"
                user_not_found:
                  summary: User not found
                  value:
                    message: "User not found"
        422:
          description: User is not a manager of the announcement
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "User is not a manager of this announcement"
        500:
          description: Internal server error

  /announcements/{announcementId}/groups/{groupId}/user/{userId}:
    post:
      tags:
        - Announcements
      summary: Add a user to an announcement group
      description: |
        Add a user to a specific group within an announcement.

        **Authorization Rules:**
        - **Administrators**: Can add any user to any announcement group
        - **Manager (Creator)**: Can add users to announcements they created
        - **Manager (Shared)**: Can add users to announcements shared with them (when sharing is enabled)

        **Business Rules:**
        - User must not already be in the announcement (any group)
        - Group must not exceed its maximum size limit
        - All entities (announcement, group, user) must exist
        - Manager users can only add users they manage (filtered by their assigned filters)
      operationId: postAnnouncementGroupUser
      parameters:
        - name: announcementId
          in: path
          description: Announcement ID containing the group
          required: true
          schema:
            type: integer
            minimum: 1
            example: 1
        - name: groupId
          in: path
          description: Group ID within the announcement
          required: true
          schema:
            type: integer
            minimum: 1
            example: 1
        - name: userId
          in: path
          description: User ID to add to the group
          required: true
          schema:
            type: integer
            minimum: 1
            example: 1
      responses:
        201:
          description: User successfully added to announcement group
          content:
            application/json:
              schema:
                type: object
                properties: {}
              example: {}
        400:
          description: Validation errors (invalid announcementId, groupId, or userId)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        401:
          description: Unauthorized - Authentication token missing or invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        403:
          description: |
            Forbidden - User doesn't have permission to add users to this announcement group
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        404:
          description: Announcement, group, or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        422:
          description: |
            Unprocessable entity - User already in announcement or maximum group size reached
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        500:
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "An unexpected error occurred"
                code: "INTERNAL_SERVER_ERROR"

  /lti/registrations:
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Registrations endpoint is available
    get:
      tags:
        - LTI
      summary: Get LTI Registrations
      description: Get all LTI Registrations filtered by params
      operationId: getLtiRegistrations
      parameters:
        - name: id
          in: query
          required: false
          schema:
            $ref: '#/components/schemas/Uuid'
        - name: name
          in: query
          required: false
          schema:
            type: string
        - name: client_id
          in: query
          required: false
          schema:
            type: string
      responses:
        200:
          description: List of registrations
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/LtiRegistration'
        400:
          description: Bad request
        401:
          description: Unauthorized
        403:
          description: Forbidden
        5XX:
          description: Internal Server Error
    post:
      tags:
        - LTI
      summary: POST a registration entity
      description: Save a new registration
      operationId: postLtiRegistration
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                client_id:
                  type: string
                  required: true
      responses:
        201:
          description: 'LTI Registration created'
        400:
          description: 'Body validation failed'
        401:
          description: 'Unauthorized'
        403:
          description: 'Forbidden'
        422:
          description: 'Unprocessable entity'
        5XX:
          description: 'Internal server error'
  /lti/registrations/{registrationId}:
    parameters:
      - name: registrationId
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/Uuid'
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Tool endpoint is available
    get:
      tags:
        - LTI
      summary: GET Lti Registration
      description: Get all information related to a registration
      operationId: getLtiRegistration
      responses:
        200:
          description: Registration data
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: '#/components/schemas/LtiRegistration'
        400:
          description: Bad request
        401:
          description: Unauthorized
        403:
          description: Forbidden
        5XX:
          description: Internal Server Error

  /lti/registrations/{registrationId}/tool:
    parameters:
      - name: registrationId
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/Uuid'
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Tool endpoint is available
    post:
      tags:
        - LTI
      summary: POST Lti Tool
      description: Save tool registry for a registration
      operationId: postLtiTool
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                audience:
                  type: string
                  required: true
                oidc_initiation_url:
                  $ref: '#/components/schemas/Url'
                launch_url:
                  $ref: '#/components/schemas/Url'
                deep_linking_url:
                  $ref: '#/components/schemas/Url'
                jwks_url:
                  $ref: '#/components/schemas/Url'
      responses:
        201:
          description: Lti Tool created
        400:
          description: Body validation failed
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Lti Registration not found
        5XX:
          description: Internal Server Error
  /lti/registrations/{registrationId}/platform:
    parameters:
      - name: registrationId
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/Uuid'
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Platform endpoint is available
    post:
      tags:
        - LTI
      summary: POST Lti Platform
      description: Save platform registry for a registration
      operationId: postLtiPlatform
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                audience:
                  type: string
                  required: true
                oidc_authentication_url:
                  $ref: '#/components/schemas/Url'
                oauth2_access_token_url:
                  $ref: '#/components/schemas/Url'
                jwks_url:
                  $ref: '#/components/schemas/Url'
      responses:
        201:
          description: Lti Platform created
        400:
          description: Body validation failed
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Lti Registration not found
        5XX:
          description: Internal Server Error
  /lti/registrations/{registrationId}/deployments:
    parameters:
      - name: registrationId
        in: path
        required: true
        schema:
          $ref: '#/components/schemas/Uuid'
    options:
      tags:
        - LTI
      summary: Check if the endpoint is available
      responses:
        204:
          description: LTI Deployment endpoint is available
    post:
      tags:
        - LTI
      summary: POST Lti Deployment
      description: Save deployment registry for a registration
      operationId: postLtiDeployment
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                deployment_id:
                  type: string
                  required: true
      responses:
        201:
          description: Lti Deployment created
        400:
          description: Body validation failed
        401:
          description: Unauthorized
        403:
          description: Forbidden
        404:
          description: Lti Registration not found
        5XX:
          description: Internal Server Error

  /purchasable-items:
    get:
      tags:
        - Purchasable Items
      summary: Get purchasable items list
      operationId: getPurchasableItems
      parameters:
        - name: page
          in: query
          description: Page number (starts at 1)
          required: false
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: page_size
          in: query
          description: Number of items per page
          required: false
          schema:
            type: integer
            minimum: 1
            default: 10
        - name: search
          in: query
          description: Search term to filter items by name or description
          required: false
          schema:
            type: string
            maxLength: 255
        - name: type
          in: query
          description: Filter by resource type
          required: false
          schema:
            type: string
            enum: [ course, subscription ]
        - name: is_active
          in: query
          description: Filter by active status
          required: false
          schema:
            type: string
            enum: [ true, false ]
        - name: price_min
          in: query
          description: Minimum price filter (in cents/centavos)
          required: false
          schema:
            type: string
            pattern: '^\d+$'
        - name: price_max
          in: query
          description: Maximum price filter (in cents/centavos)
          required: false
          schema:
            type: string
            pattern: '^\d+$'
        - name: sort_by
          in: query
          description: Field to sort by
          required: false
          schema:
            type: string
            enum: [ id, name, description, price_amount, is_active, created_at, updated_at ]
        - name: sort_dir
          in: query
          description: Sort direction (required when sort_by is provided)
          required: false
          schema:
            type: string
            enum: [ asc, desc ]
            default: asc
      responses:
        '200':
          description: List of purchasable items
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PurchasableItem'
                  metadata:
                    type: object
                    properties:
                      page:
                        type: integer
                        description: Current page number
                        example: 1
                      total_pages:
                        type: integer
                        description: Total number of pages
                        example: 5
                      limit:
                        type: integer
                        description: Items per page
                        example: 10
                      total:
                        type: integer
                        description: Total number of items
                        example: 47
                    description: Pagination metadata (only present when pagination parameters are provided)
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          description: Internal server error
    put:
      tags:
        - Purchasable Items
      summary: Create a purchasable item
      operationId: putPurchasableItem
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - type
                - resource_id
                - price_amount
              properties:
                type:
                  type: string
                  enum: [ course, subscription ]
                  description: Type of resource to make purchasable
                resource_id:
                  type: integer
                  minimum: 1
                  description: ID of the resource (course or subscription)
                price_amount:
                  type: integer
                  minimum: 0
                  description: Price amount in cents/centavos
                  example: 2500
                price_currency:
                  type: string
                  enum: [ EUR, USD ]
                  description: Currency code (defaults to system currency if not provided)
                  example: "EUR"
      responses:
        '201':
          description: Purchasable item created successfully
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden - Insufficient permissions
        '422':
          description: Unprocessable entity - Resource not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Internal server error

  /purchasable-items/{purchasableItemId}:
    get:
      tags:
        - Purchasable Items
      summary: Get a specific purchasable item
      operationId: getPurchasableItem
      parameters:
        - name: purchasableItemId
          in: path
          description: UUID of the purchasable item to retrieve
          required: true
          schema:
            $ref: '#/components/schemas/Uuid'
      responses:
        '200':
          description: Purchasable item details
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    $ref: '#/components/schemas/PurchasableItem'
        '400':
          description: Invalid UUID format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '404':
          description: Purchasable item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Purchasable item not found"
        '500':
          description: Internal server error
    patch:
      tags:
        - Purchasable Items
      summary: Partially update a purchasable item
      operationId: patchPurchasableItem
      parameters:
        - name: purchasableItemId
          in: path
          description: UUID of the purchasable item to update
          required: true
          schema:
            $ref: '#/components/schemas/Uuid'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                price_amount:
                  type: integer
                  minimum: 0
                  description: Price amount in cents/centavos (required if price_currency is provided)
                  example: 2500
                price_currency:
                  type: string
                  enum: [ EUR, USD ]
                  description: Currency code (required if price_amount is provided)
                  example: "EUR"
                is_active:
                  type: boolean
                  description: Whether the purchasable item is active
                  example: true
      responses:
        '204':
          description: Purchasable item updated successfully
        '400':
          description: Validation errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationError'
        '401':
          description: Unauthorized
        '422':
          description: Unprocessable entity - Purchasable item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
              example:
                message: "Purchasable item not found"
        '500':
          description: Internal server error
    delete:
      tags:
        - Purchasable Items
      summary: Delete a purchasable item
      operationId: deletePurchasableItem
      parameters:
        - name: purchasableItemId
          in: path
          required: true
          description: UUID of the purchasable item to delete
          schema:
            $ref: '#/components/schemas/Uuid'
      responses:
        '204':
          description: Purchasable item deleted successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '5XX':
          description: Infrastructure errors

  /roles:
    parameters:
      - name: allowed_to_manage
        in: query
        description:
          Optional Param to filter Roles by the request user.
        required: false
        schema:
          type: string
          enum: ['true']
        example: 'true'
    get:
      tags:
        - Roles
      summary: Get roles list
      description: |
        Returns a list of roles.
        This endpoint is accessible by users with roles: ADMIN, SUPER_ADMIN, or MANAGER.
      operationId: getRoles
      responses:
        200:
          description: List of roles
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Role'
        400:
          description: Validation error
        401:
          description: Unauthorized
        403:
          description: Forbidden - Insufficient permissions
        5XX:
          description: Infrastructure errors
  /locales:
    get:
      tags:
        - Locales
      summary: Get available locales
      description: Retrieves the list of available locales (languages) in the system
      operationId: getLocales
      responses:
        '200':
          description: List of available locales
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
                  example: "es"
                  description: "ISO language code"
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'


components:
  schemas:
    Uuid:
      type: string
      format: uuid
      example: "550e8400-e29b-41d4-a716-************"
      description: "UUID v4"
    Url:
      type: string
      format: url
      example: "https://example.com"
      description: A valid url
    Locale:
      type: object
      properties:
        code:
          type: string
          example: "es"
          description: "ISO language code"
    Pagination:
      type: object
      properties:
        page:
          type: integer
          example: 1
          description: "Page number"
        total_pages:
          type: integer
          example: 1
          description: "Total pages"
        limit:
          type: integer
          example: 10
          description: "Items per page"
        total:
          type: integer
          example: 10
          description: "Total items"
    Creator:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: User's unique ID
        name:
          type: string
          description: User's first name
        lastName:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
    AnnouncementManager:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Manager's unique ID
        name:
          type: string
          description: Manager's first name
        lastName:
          type: string
          description: Manager's last name
        email:
          type: string
          format: email
          description: Manager's email address
      required:
        - id
        - name
        - lastName
        - email
    User:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: User's unique ID
        avatar:
          type: string
          nullable: true
          description: User's avatar URL
        first_name:
          type: string
          description: User's first name
        last_name:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
        roles:
          type: array
          items:
            type: string
          description: Roles assigned to the user
        is_active:
          type: boolean
          description: Indicates if the user is active
        points:
          type: integer
          nullable: true
          description: User's points
        actions:
          type: object
          description: Available actions for the user based on permissions
    UserFilter:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Filter's unique ID
        name:
          type: string
          description: Filter name
        category_id:
          type: integer
          format: int64
          description: Filter's category ID
    ManagerFilter:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: Filter's unique ID
        name:
          type: string
          description: Filter name
        category_id:
          type: integer
          format: int64
          description: Filter's category ID
    MinimalUser:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: User's unique ID
        first_name:
          type: string
          description: User's first name
        last_name:
          type: string
          description: User's last name
        email:
          type: string
          format: email
          description: User's email address
    Error:
      type: object
      properties:
        message:
          type: string
          description: Error message
    ValidationError:
      type: object
      properties:
        message:
          type: string
          description: Validation error message
        metadata:
          type: object
          properties:
            violations:
              type: object
              additionalProperties:
                type: string
              description: Field validation violations
    Role:
      type: string
      enum: [ ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_TUTOR, ROLE_MANAGER, ROLE_CREATOR, ROLE_USER ]
    LtiRegistration:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        name:
          type: string
          description: Registration name
        client_id:
          type: string
          description: Unique client id
        tool:
          $ref: "#/components/schemas/LtiTool"
        platform:
          $ref: "#/components/schemas/LtiPlatform"
        deployments:
          type: array
          items:
            $ref: "#/components/schemas/LtiDeployment"

    LtiTool:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        name:
          type: string
          description: Tool Name
        audience:
          type: string
          description: A Valid audience for LTI
        oidc_initiation_url:
          $ref: '#/components/schemas/Url'
        launch_url:
          $ref: '#/components/schemas/Url'
        deep_linking_url:
          $ref: '#/components/schemas/Url'
        jwks_url:
          $ref: '#/components/schemas/Url'

    LtiPlatform:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        name:
          type: string
          description: Platform Name
          default: EasyLearning
        audience:
          type: string
          description: A Valid audience for LTI
        oidc_authentication_url:
          $ref: '#/components/schemas/Url'
        oauth2_access_token_url:
          $ref: '#/components/schemas/Url'
        jwks_url:
          $ref: '#/components/schemas/Url'
    LtiDeployment:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        name:
          type: string
          description: Deployment Name
        deployment_id:
          type: string
          description: Unique identifier in registration context
    Filter:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 'Filter ID'
        name:
          type: string
          description: 'Filter name'
    FilterCategory:
      type: object
      properties:
        id:
          type: integer
          format: int64
          description: 'Filter Category ID'
        name:
          type: string
          description: 'Category name'

    PurchasableItem:
      type: object
      properties:
        id:
          $ref: "#/components/schemas/Uuid"
        name:
          type: string
          description: Name of the purchasable item
          example: "Advanced JavaScript Course"
        description:
          type: string
          description: Description of the purchasable item
          example: "Learn advanced JavaScript concepts and patterns"
        price_amount:
          type: integer
          description: Price amount in cents/centavos
          example: 2500
          minimum: 0
        price_currency:
          type: string
          description: Price currency code
          example: "EUR"
          pattern: "^[A-Z]{3}$"
        resource_type:
          type: string
          description: Type of the resource being sold
          enum: [course, subscription]
          example: "course"
        resource_id:
          type: string
          description: ID of the resource being sold
          example: "550e8400-e29b-41d4-a716-************"
        is_active:
          type: boolean
          description: Whether the item is currently active and available for purchase
          example: true
      required:
        - id
        - name
        - description
        - price_amount
        - price_currency
        - resource_type
        - resource_id
        - is_active

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  responses:
    Unauthorized:
      description: User not authenticated or authentication token has expired
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: "Unauthorized"

    Forbidden:
      description: User does not have enough permissions to access this resource
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: "Forbidden access"

    BadRequest:
      description: Bad request - Invalid input or malformed request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationError'
          example:
            message: "Validation failed"
            metadata:
              violations:
                "[uuid]": ["This is not a valid UUID."]

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            message: "Purchasable item not found"

    ValidationError:
      description: Validation error - Request data does not meet requirements
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationError'
          example:
            message: "Validation failed"
            metadata:
              violations:
                "[resource_type]": ["This field is missing."]
                "[price_amount]": ["This value should be of type digit."]
