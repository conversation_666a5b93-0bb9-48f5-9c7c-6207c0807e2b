<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\User\UserFilter;

use App\Tests\V2\Mother\User\UserFilter\UserFilterMother;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\Shared\Id\IdCollection;
use App\V2\Domain\User\Exception\UserFilterNotFoundException;
use App\V2\Domain\User\Exception\UserFilterRepositoryException;
use App\V2\Domain\User\UserFilter\UserFilterCriteria;
use App\V2\Domain\User\UserFilter\UserFilterRepository;
use PHPUnit\Framework\TestCase;

abstract class UserFilterRepositoryTestCase extends TestCase
{
    abstract protected function getRepository(): UserFilterRepository;

    /**
     * @throws InfrastructureException
     * @throws UserFilterRepositoryException
     */
    public function testInsert(): void
    {
        $user1Filter1 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );
        $duplicate = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $result = $repository->findBy(UserFilterCriteria::createEmpty());

        $this->assertCount(4, $result);

        try {
            $repository->insert($duplicate);
            $this->fail('Expected an exception to be thrown.');
        } catch (UserFilterRepositoryException $e) {
            $this->assertEquals(UserFilterRepositoryException::duplicateFilter(
                userId: new Id(1),
                filterId: new Id(1)
            ), $e);
        }
    }

    public function testFindOneBy(): void
    {
        $user1Filter1 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $this->assertEquals(
            $user1Filter1,
            $repository->findOneBy(UserFilterCriteria::createEmpty()
                ->filterByUserId(new Id(1))
                ->filterByFilterId(new Id(1)))
        );

        $this->assertEquals(
            $user2Filter1,
            $repository->findOneBy(UserFilterCriteria::createEmpty()
                ->filterByUserId(new Id(2)))
        );

        $this->assertEquals(
            $user1Filter2,
            $repository->findOneBy(UserFilterCriteria::createEmpty()
                ->filterByFilterId(new Id(2)))
        );

        try {
            $repository->findOneBy(UserFilterCriteria::createEmpty()->filterByUserId(new Id(3)));
            $this->fail('Expected an exception to be thrown.');
        } catch (UserFilterNotFoundException $e) {
            $this->assertEquals(new UserFilterNotFoundException(), $e);
        }

        try {
            $repository->findOneBy(UserFilterCriteria::createEmpty()->filterByFilterId(new Id(3)));
            $this->fail('Expected an exception to be thrown.');
        } catch (UserFilterNotFoundException $e) {
            $this->assertEquals(new UserFilterNotFoundException(), $e);
        }
    }

    /**
     * @throws InfrastructureException
     * @throws UserFilterRepositoryException
     */
    public function testFindBy(): void
    {
        $user1Filter1 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $result = $repository->findBy(UserFilterCriteria::createEmpty());
        $this->assertCount(4, $result);

        $result = $repository->findBy(UserFilterCriteria::createEmpty()
            ->filterByUserId(new Id(1)));
        $this->assertCount(2, $result);
        $this->assertCount(0, array_diff([$user1Filter1, $user1Filter2], $result->all()));

        $result = $repository->findBy(UserFilterCriteria::createEmpty()
            ->filterByUserId(new Id(2)));
        $this->assertCount(2, $result);
        $this->assertCount(0, array_diff([$user2Filter1, $user2Filter2], $result->all()));

        $result = $repository->findBy(UserFilterCriteria::createEmpty()
            ->filterByUserId(new Id(1))
            ->filterByFilterId(new Id(1)));
        $this->assertCount(1, $result);
        $this->assertEquals($user1Filter1, $result->first());

        $result = $repository->findBy(
            UserFilterCriteria::createEmpty()
                ->filterByFilterIds(new IdCollection([new Id(1)]))
        );
        $this->assertCount(2, $result);
        $this->assertCount(
            2,
            array_intersect([$user1Filter1, $user2Filter1], $result->all())
        );

        $result = $repository->findBy(
            UserFilterCriteria::createEmpty()
                ->filterByFilterIds(new IdCollection([new Id(2)]))
        );
        $this->assertCount(2, $result);
        $this->assertCount(
            2,
            array_intersect([$user1Filter2, $user2Filter2], $result->all())
        );
    }

    public function testDelete(): void
    {
        $user1Filter1 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(1)
        );
        $user1Filter2 = UserFilterMother::create(
            userId: new Id(1),
            filterId: new Id(2)
        );
        $user2Filter1 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(1)
        );
        $user2Filter2 = UserFilterMother::create(
            userId: new Id(2),
            filterId: new Id(2)
        );

        $repository = $this->getRepository();

        $repository->insert($user1Filter1);
        $repository->insert($user1Filter2);
        $repository->insert($user2Filter1);
        $repository->insert($user2Filter2);

        $this->assertCount(4, $repository->findBy(UserFilterCriteria::createEmpty()));

        $repository->delete($user1Filter1);
        $result = $repository->findBy(UserFilterCriteria::createEmpty());
        $this->assertCount(3, $result);
        $this->assertCount(0, array_diff([$user1Filter2, $user2Filter1, $user2Filter2], $result->all()));
    }
}
