get_health_check:
  path: /health
  methods: GET
  controller: App\V2\Infrastructure\Controller\HealthCheckController

get_campus_launch_lti_chapter:
  path: /courses/{courseId}/chapter/{chapterId}/lti-launch
  methods: GET
  controller: App\V2\Infrastructure\Controller\Admin\GetLaunchLtiChapterController

get_campus_locales:
  path: /locales
  methods: GET
  controller: App\V2\Infrastructure\Controller\Campus\GetLocalesController

get_purchases:
  path: /purchases
  methods: GET
  controller: App\V2\Infrastructure\Controller\Campus\GetPurchasesController

post_create_purchase:
  path: /purchases
  methods: POST
  controller: App\V2\Infrastructure\Controller\Campus\PostCreatePurchaseController

get_campus_purchase:
  path: /purchases/{purchaseId}
  methods: GET
  controller: App\V2\Infrastructure\Controller\Campus\GetPurchaseController
