<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Validator\Admin;

use App\Tests\V2\Infrastructure\Validator\ValidatorTestCase;
use App\V2\Infrastructure\Validator\Admin\CreateCourseValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\Attributes\DataProvider;

class CreateCourseValidatorTest extends ValidatorTestCase
{
    /**
     * @throws ValidatorException
     */
    #[DataProvider('createCourseValidatorSuccessProvider')]
    public function testCreateCourseValidatorSuccess(array $payload): void
    {
        $this->expectNotToPerformAssertions();

        CreateCourseValidator::validateCreateCourseRequest($payload);
    }

    public static function createCourseValidatorSuccessProvider(): \Generator
    {
        yield 'valid name, code, locale, duration, category and typeCourse' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
        ];

        yield 'valid name, code, locale, duration, category and typeCourse with null duration' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => null,
                'category' => 1,
                'typeCourse' => 1,
            ],
        ];

        yield 'valid name, code, locale, duration, category and typeCourse with string general information' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'general-information' => 'This is a test course.',
            ],
        ];

        yield 'valid name, code, locale, duration, category and typeCourse with string description' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'description' => 'Detailed description of the course.',
            ],
        ];

        yield 'valid name, code, locale, duration, category and typeCourse with string tags' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'tags' => 'tag1,tag2,tag3',
            ],
        ];

        yield 'valid name, code, locale, duration, category and typeCourse with string documentation' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'documentation' => 'Course documentation link',
            ],
        ];

        yield 'valid name, code, locale, duration, category and typeCourse with string segments' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'segments' => 'segment1,segment2',
            ],
        ];

        yield 'valid payload with all optional fields' => [
            'payload' => [
                'name' => 'Complete Test Course',
                'code' => 'CTC101',
                'locale' => 'en',
                'duration' => 2,
                'category' => 2,
                'typeCourse' => 2,
                'general-information' => 'Complete course information',
                'description' => 'Complete course description',
                'tags' => 'complete,test,course',
                'documentation' => 'Complete documentation',
                'segments' => 'segment1,segment2,segment3',
            ],
        ];

        yield 'valid payload with string duration' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => '1',
                'category' => 1,
                'typeCourse' => 1,
            ],
        ];

        yield 'valid payload with string category and typeCourse' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => '1',
                'typeCourse' => '1',
            ],
        ];
    }

    #[DataProvider('createCourseValidatorFailProvider')]
    public function testCreateCourseValidatorFail(array $payload, array $violations): void
    {
        try {
            CreateCourseValidator::validateCreateCourseRequest($payload);
            $this->fail('Expected ValidatorException was not thrown');
        } catch (ValidatorException $e) {
            $this->assertViolations($violations, $e->getViolations());
        }
    }

    public static function createCourseValidatorFailProvider(): \Generator
    {
        yield 'missing name' => [
            'payload' => [
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[name]' => 'This field is missing.',
            ],
        ];
        yield 'wrong name value' => [
            'payload' => [
                'name' => 123,
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
            ],
        ];
        yield 'missing code' => [
            'payload' => [
                'name' => 'Test Course',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[code]' => 'This field is missing.',
            ],
        ];
        yield 'wrong code value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 123123,
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[code]' => 'This value should be of type string.',
            ],
        ];
        yield 'missing locale' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 123,
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[locale]' => 'This value should be of type string.',
            ],
        ];
        yield 'missing duration' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[duration]' => 'This field is missing.',
            ],
        ];
        yield 'wrong duration value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 'invalid',
                'category' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[duration]' => 'This value must be null or numeric.',
            ],
        ];
        yield 'missing category' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'typeCourse' => 1,
            ],
            'violations' => [
                '[category]' => 'This field is missing.',
            ],
        ];
        yield 'wrong category value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 'invalid',
                'typeCourse' => 1,
            ],
            'violations' => [
                '[category]' => 'This value should be of type numeric.',
            ],
        ];
        yield 'missing typeCourse' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
            ],
            'violations' => [
                '[typeCourse]' => 'This field is missing.',
            ],
        ];
        yield 'wrong typeCourse value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 'invalid',
            ],
            'violations' => [
                '[typeCourse]' => 'This value should be of type numeric.',
            ],
        ];
        yield 'wrong general-information value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'general-information' => 123,
            ],
            'violations' => [
                '[general-information]' => 'This value should be of type string.',
            ],
        ];
        yield 'wrong description value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'description' => 123,
            ],
            'violations' => [
                '[description]' => 'This value should be of type string.',
            ],
        ];
        yield 'empty tags value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'tags' => '',
            ],
            'violations' => [
                '[tags]' => 'This value should not be blank.',
            ],
        ];
        yield 'wrong tags value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'tags' => 123,
            ],
            'violations' => [
                '[tags]' => 'This value should be of type string.',
            ],
        ];
        yield 'wrong documentation value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'documentation' => 123,
            ],
            'violations' => [
                '[documentation]' => 'This value should be of type string.',
            ],
        ];
        yield 'wrong segments value' => [
            'payload' => [
                'name' => 'Test Course',
                'code' => 'TC101',
                'locale' => 'es',
                'duration' => 1,
                'category' => 1,
                'typeCourse' => 1,
                'segments' => 123,
            ],
            'violations' => [
                '[segments]' => 'This value should be of type string.',
            ],
        ];
        yield 'multiple violations' => [
            'payload' => [
                'name' => 123,
                'code' => 456,
                'locale' => 789,
                'duration' => 'invalid',
                'category' => 'invalid',
                'typeCourse' => 'invalid',
                'general-information' => 123,
                'description' => 456,
                'tags' => 789,
                'documentation' => 123,
                'segments' => 456,
            ],
            'violations' => [
                '[name]' => 'This value should be of type string.',
                '[code]' => 'This value should be of type string.',
                '[locale]' => 'This value should be of type string.',
                '[duration]' => 'This value must be null or numeric.',
                '[category]' => 'This value should be of type numeric.',
                '[typeCourse]' => 'This value should be of type numeric.',
                '[general-information]' => 'This value should be of type string.',
                '[description]' => 'This value should be of type string.',
                '[tags]' => 'This value should be of type string.',
                '[documentation]' => 'This value should be of type string.',
                '[segments]' => 'This value should be of type string.',
            ],
        ];
    }
}
