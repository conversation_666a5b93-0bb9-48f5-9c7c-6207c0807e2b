<?php

declare(strict_types=1);

namespace App\Tests\V2\Domain\Purchase;

use App\V2\Domain\Purchase\PaginatedPurchasableItemCollection;
use App\V2\Domain\Purchase\PurchasableItemCollection;
use App\V2\Domain\Shared\Collection\Collection;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\User\PaginatedUserCollection;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class PaginatedPurchasableItemCollectionTest extends TestCase
{
    /**
     * @throws CollectionException
     */
    public function testConstructor(): void
    {
        $collection = new PurchasableItemCollection([]);
        $paginatedCollection = new PaginatedPurchasableItemCollection($collection, 100);

        $this->assertSame($collection, $paginatedCollection->getCollection());
        $this->assertSame(100, $paginatedCollection->getTotalItems());
    }

    /**
     * @throws CollectionException
     */
    public function testGetCollection(): void
    {
        $collection = new PurchasableItemCollection([]);
        $paginatedCollection = new PaginatedPurchasableItemCollection($collection, 100);

        $this->assertSame($collection, $paginatedCollection->getCollection());
    }

    /**
     * @throws CollectionException
     */
    public function testGetTotalItems(): void
    {
        $totalItems = 100;
        $paginatedCollection = new PaginatedPurchasableItemCollection(new PurchasableItemCollection([]), $totalItems);

        $result = $paginatedCollection->getTotalItems();
        $this->assertSame($totalItems, $result);
    }

    /**
     * @throws Exception
     */
    public function testConstructorThrowsExceptionForInvalidCollection(): void
    {
        $invalidCollection = $this->createMock(Collection::class);
        $this->expectException(\TypeError::class);

        new PaginatedUserCollection($invalidCollection, 100);
    }
}
