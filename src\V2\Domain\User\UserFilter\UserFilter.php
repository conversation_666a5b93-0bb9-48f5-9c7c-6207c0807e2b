<?php

declare(strict_types=1);

namespace App\V2\Domain\User\UserFilter;

use App\V2\Domain\Shared\Entity\Entity;
use App\V2\Domain\Shared\Id\Id;
use App\V2\Domain\User\Filter;

class UserFilter implements Entity
{
    private ?Filter $filter = null;

    public function __construct(
        private readonly Id $userId,
        private readonly Id $filterId,
    ) {
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }

    public function getFilterId(): Id
    {
        return $this->filterId;
    }

    public function getFilter(): ?Filter
    {
        return $this->filter;
    }

    public function setFilter(Filter $filter): self
    {
        $this->filter = $filter;

        return $this;
    }

    public function __toString(): string
    {
        return "{$this->userId}:{$this->filterId}";
    }
}
