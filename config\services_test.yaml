imports:
  - { resource: services/test/sso.yaml }

  - { resource: services/test/ranking.yaml }

  - { resource: services/test/easylearning.yaml }

  - { resource: services/test/announcement.yaml }

parameters:
  app.authentication_logs: ''

services:
  App\EntityListener\DeletedByListener:
    tags: [ ]

  App\Tests\Functional\Mock\InMemoryMailer:
    public: true

  Symfony\Component\Mailer\MailerInterface:
    alias: App\Tests\Functional\Mock\InMemoryMailer
    public: true

  ## V2 Services
  App\V2\Application\Log\Logger:
    alias: 'App\V2\Infrastructure\Log\MonologLogger'


  ## Repositories
  App\V2\Domain\User\UserRepository:
    class: App\V2\Infrastructure\Persistence\User\DoctrineUserRepository
    public: true
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\Creator\CourseCreatorRepository:
    alias: App\V2\Infrastructure\Persistence\Course\Creator\InMemoryCourseCreatorRepository

  App\V2\Domain\Security\RefreshTokenRepository:
    alias: App\V2\Infrastructure\Persistence\Security\InMemoryRefreshTokenRepository

  App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository:
    alias: App\V2\Infrastructure\Persistence\Announcement\Manager\InMemoryAnnouncementManagerRepository

  App\V2\Domain\Purchase\PurchasableItemRepository:
    alias: App\V2\Infrastructure\Persistence\Purchase\InMemoryPurchasableItemRepository
    public: true

  App\V2\Domain\Purchase\PurchaseRepository:
    alias: App\V2\Infrastructure\Persistence\Purchase\InMemoryPurchaseRepository
    public: true

  App\V2\Domain\Subscription\SubscriptionRepository:
    alias: App\V2\Infrastructure\Persistence\Subscription\InMemorySubscriptionRepository

  App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository:
    alias: App\V2\Infrastructure\Persistence\Filter\FilterCategory\InMemoryFilterCategoryRepository

  App\V2\Domain\Filter\FilterRepository:
    alias: App\V2\Infrastructure\Persistence\Filter\InMemoryFilterRepository

  App\V2\Domain\User\UserFilter\UserFilterRepository:
    alias: App\V2\Infrastructure\Persistence\User\UserFilter\InMemoryUserFilterRepository

  App\V2\Domain\User\ManagerFilter\ManagerFilterRepository:
    alias: App\V2\Infrastructure\Persistence\User\ManagerFilter\InMemoryManagerFilterRepository

  App\V2\Infrastructure\Security\LexitJwtToken:
    public: true
    arguments:
      - '@lexik_jwt_authentication.jwt_manager'
      - '@App\V2\Domain\Security\RefreshTokenRepository'
      - '@App\V2\Domain\User\UserRepository'
      - 10

  App\V2\Domain\LTI\LtiKeyProvider:
    alias: App\V2\Infrastructure\LTI\InMemoryLtiKeyProvider

  App\V2\Domain\LTI\LtiRegistrationRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiRegistrationRepository
  App\V2\Domain\LTI\LtiDeploymentRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiDeploymentRepository
    public: true
  App\V2\Domain\LTI\LtiToolRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiToolRepository
  App\V2\Domain\LTI\LtiPlatformRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\InMemoryLtiPlatformRepository
    
  App\V2\Domain\VirtualMeeting\VirtualMeetingRepository:
    alias: App\V2\Infrastructure\Persistence\VirtualMeeting\InMemoryVirtualMeetingRepository

  OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface:
    alias: App\V2\Infrastructure\Persistence\LTI\RegistrationRepository

  OAT\Library\Lti1p3Core\Security\Key\KeyChainRepositoryInterface:
    alias: App\V2\Infrastructure\Persistence\LTI\KeyChainRepository

  OAT\Library\Lti1p3Core\Security\User\UserAuthenticatorInterface:
    alias: App\V2\Infrastructure\LTI\UserAuthenticator

  OAT\Library\Lti1p3Core\Security\Oidc\OidcAuthenticator:
    class: OAT\Library\Lti1p3Core\Security\Oidc\OidcAuthenticator

  OAT\Library\Lti1p3Core\Message\Launch\Builder\LtiResourceLinkLaunchRequestBuilder:
    class: OAT\Library\Lti1p3Core\Message\Launch\Builder\LtiResourceLinkLaunchRequestBuilder

  OAT\Library\Lti1p3Core\Message\Launch\Builder\PlatformOriginatingLaunchBuilder:
    class: OAT\Library\Lti1p3Core\Message\Launch\Builder\PlatformOriginatingLaunchBuilder

  OAT\Library\Lti1p3Core\Security\Jwt\Builder\Builder:
    class: OAT\Library\Lti1p3Core\Security\Jwt\Builder\Builder
